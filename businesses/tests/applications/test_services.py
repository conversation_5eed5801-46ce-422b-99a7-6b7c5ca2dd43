from django.test import TestCase, override_settings
from django.core.cache import cache
from django.contrib.auth.models import User
from base.factory import UserFactory
from businesses.factory import CompanyFactory
from businesses.services.applications import BusinessApplicationService
from candidates.factory import ApplicationFactory, CandidateFactory
from candidates.models import Interview
from jobs.factory import JobFactory


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestBusinessApplicationService(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.user = UserFactory()
        self.service = BusinessApplicationService()
        self.business = CompanyFactory(created_by=self.user)
        self.job = JobFactory(
            company_name=self.business,
        )

        # create 10 users
        self.users = [
            User.objects.create_user(
                username=f"testuser{i}",
                email=f"testuser{i}@example.com",
                password="password123",
            )
            for i in range(10)
        ]

        # create candidates
        self.candidates = [
            CandidateFactory(
                user=user,
            )
            for user in self.users
        ]

        # apply to the job
        for candidate in self.candidates:
            ApplicationFactory(
                applicant=candidate,
                job_applied=self.job,
                status="Pending",
            )

    def test_get_all_applications_success(self):
        response = self.service.get_applications_by_job_id(
            business_slug=self.business.slug,
            user=self.user,
            job_id=self.job.id,
        )
        print(response)
        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data["results"]), 10)

    # test get  page size
    def test_get_all_applications_page_size(self):
        response = self.service.get_applications_by_job_id(
            business_slug=self.business.slug,
            user=self.user,
            job_id=self.job.id,
            page_size=3,
        )
        print(response)
        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data["results"]), 3)

    # test search query by email
    def test_get_all_applications_search_query(self):
        response = self.service.get_applications_by_job_id(
            business_slug=self.business.slug,
            user=self.user,
            job_id=self.job.id,
            search_query="testuser3",
        )
        print(response)
        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data["results"]), 1)

    # test invalid job id
    def test_get_all_applications_invalid_job_id(self):
        response = self.service.get_applications_by_job_id(
            business_slug=self.business.slug,
            user=self.user,
            job_id=999,
        )
        print(response)
        self.assertEqual(response.success, False)

    # test invalid user
    def test_get_all_applications_invalid_user(self):
        response = self.service.get_applications_by_job_id(
            business_slug=self.business.slug,
            user=UserFactory(),
            job_id=self.job.id,
        )
        print(response)
        self.assertEqual(response.success, False)



class TestBusinessApplication(TestCase):
    def setUp(self):
        self.service = BusinessApplicationService()
        self.user = UserFactory()
        self.user_1 = UserFactory()
        self.candidate = CandidateFactory(user=self.user)
        self.company = CompanyFactory(created_by=self.user_1)
        self.job = JobFactory(company_name=self.company)
        self.application = ApplicationFactory(
            applicant=self.candidate,
            job_applied=self.job,
            status="Pending",
        )

    def test_update_application_status_success(self):
        data = {"status": "Shortlisted"}
        response = self.service.update_application(
            self.company.slug,
            self.application.id,
            data,
        )

        self.assertTrue(response.success)
        self.assertEqual(response.data["status"], "Shortlisted")

    def test_update_application_to_interview_success(self):
        data = {
            "status": "Interview",
            "interview": {
                "interview_date": "2024-01-01 10:00:00",
                "from_time": "10:00:00",
                "to_time": "11:00:00",
                "location": "Office",
                "message": "Please prepare for the interview.",
            },
        }
        response = self.service.update_application(
            business_slug=self.company.slug,
            application_id=self.application.id,
            data=data,
        )

        self.assertTrue(response.success)
        self.assertIn("interview", response.data)
        self.assertIn("application", response.data)

    def test_update_application_invalid_application_id(self):
        data = {"status": "Shortlisted"}
        response = self.service.update_application(
            self.company.slug,
            999,
            data,
        )

        self.assertFalse(response.success)

    def test_update_application_missing_status(self):
        data = {}
        response = self.service.update_application(
            self.company.slug,
            self.application.id,
            data,
        )

        self.assertFalse(response.success)

    def test_update_application_status_to_rejected(self):
        data = {"status": "Rejected"}
        response = self.service.update_application(
            self.company.slug,
            self.application.id,
            data,
        )

        self.assertTrue(response.success)
        self.assertEqual(response.data["status"], "Rejected")
    
    def test_get_all_business_applications(self):
        response = self.service.get_all_business_applications(
            self.user_1,
            self.company.slug,
        )
        self.assertTrue(response.success)
    
    def test_get_all_business_applications_nonexistent(self):
        response = self.service.get_all_business_applications(
            self.user_1,
            "nonexistent-company",
        )
        self.assertFalse(response.success)

