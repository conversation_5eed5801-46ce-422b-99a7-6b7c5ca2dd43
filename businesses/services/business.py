import copy
from datetime import datetime
from operator import truediv
import re
from urllib import response
from zoneinfo import ZoneInfo
from django.db import transaction
from typing import Dict, Any, List, Optional
from django.core.exceptions import ValidationError
from accounts.services.profile_service import APIResponse
from base.services.logging import LoggingService
from base.utils.convert_date_string import convert_date_string
from base.utils.status_checker import <PERSON><PERSON><PERSON><PERSON>
from businesses.models import Company, Service
from businesses.repositories.business_repository import CompanyRepository
from businesses.services.service import BusinessServiceService
from candidates.models import Application, Candidate
from candidates.repositories.application_repository import RepositoryResponse
from candidates.views.serializers import CandidateSerializer, GetCandidateSerializer
from rest_framework import status
from businesses.views.serializers import BusinessSerializer
from base.services.language import LanguageService
from base.services.social_media import SocialMediaPlatformService
from base.models import Category as SkillsCategory
from candidates.views import data
from django.core.paginator import Paginator, EmptyPage, InvalidPage
from django.db.models import Q
from base.services.caching import CachingService

logging_service = LoggingService()


class BusinessService:
    def __init__(self):
        self.cache = CachingService()
        self.repository = CompanyRepository()
        self.language = LanguageService()
        self.social_media = SocialMediaPlatformService()
        self.service_service = BusinessServiceService()

    def _clear_business_related_caches(self, slug=None, user_id=None, business_name=None):
        """
        Clear all caches related to a business when it's modified or deleted.

        Args:
            slug (str, optional): Business slug
            user_id (int, optional): User ID who created the business
            business_name (str, optional): Business name for name-based caches
        """
        try:
            # Clear individual business cache
            if slug:
                self.cache.deleteCache(f"business:{slug}")
                self.cache.deleteCache(f"business_profile_status:{slug}")

            # Clear user's business cache
            if user_id:
                self.cache.deleteCache(f"business_by_user:{user_id}")

            # Clear name-based cache
            if business_name:
                self.cache.deleteCache(f"businesses_by_name:{business_name.lower()}")

            # Clear paginated caches (this is a bit brute force, but ensures consistency)
            # In a production system, you might want to track cache keys more systematically
            for page in range(1, 11):  # Clear first 10 pages
                for page_size in [10, 20, 50]:  # Common page sizes
                    self.cache.deleteCache(f"all_businesses_page_{page}_size_{page_size}")

            # Clear business candidates caches if slug is provided
            if slug:
                for page in range(1, 6):  # Clear first 5 pages
                    for page_size in [10, 20]:
                        self.cache.deleteCache(f"business_candidates:{slug}_page_{page}_size_{page_size}")

        except Exception as e:
            logging_service.log_error(f"Error clearing business caches: {e}")

    def create_business(self, user, data: Dict[str, Any]) -> APIResponse:
        required_keys = ["name", "description", "business_address", "industry"]
        missing_fields = logging_service.check_required_fields(data, required_keys)
        if missing_fields:
            return APIResponse(
                success=False,
                data=None,
                message=missing_fields,
                status=status.HTTP_400_BAD_REQUEST,
            )

        existing_business = self.repository.get_businesses_by_name(data["name"])

        if existing_business.success:
            return APIResponse(
                success=False,
                message="Company with the same name already exists.",
                data=None,
                status=status.HTTP_409_CONFLICT,
            )
        try:
            if "established_at" in data:
                date_response = convert_date_string(
                    data.get("established_at"), "established_at"
                )
                if not date_response.success:
                    return APIResponse(
                        success=False,
                        data=None,
                        message=date_response.message,
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                data["established_at"] = date_response.data
            business = self.repository.create_business(user, data)
            if business.success:
                serialize_data = BusinessSerializer(business.data).data

                # Cache the new business
                self.cache.addToCacheList("businesses", serialize_data)
                # Cache individual business by slug
                self.cache.setCache(f"business:{serialize_data['slug']}", serialize_data)
                # Cache business by user
                self.cache.setCache(f"business_by_user:{user.id}", serialize_data)

                # Clear related caches to ensure consistency
                self._clear_business_related_caches(business_name=data["name"])

                return APIResponse(
                    success=True,
                    message="Company created successfully.",
                    data=serialize_data,
                    status=status.HTTP_201_CREATED,
                )
            else:
                return APIResponse(
                    success=False,
                    message=business.message,
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            # TODO: implement error logging
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to create company",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_business_by_slug(self, slug):
        try:
            # Check cache first
            cache_key = f"business:{slug}"
            cached_business = self.cache.getCache(cache_key)
            if cached_business:
                return APIResponse(
                    success=True,
                    message="Business retrieved successfully.",
                    data=cached_business,
                    status=status.HTTP_200_OK,
                )

            # If not in cache, get from database
            business = self.repository.get_business(slug)
            if business.success:
                serialize_data = BusinessSerializer(business.data).data

                # Cache the business data
                self.cache.setCache(cache_key, serialize_data)

                return APIResponse(
                    success=True,
                    message="Business retrieved successfully.",
                    data=serialize_data,
                    status=status.HTTP_200_OK,
                )
            else:
                return APIResponse(
                    success=False,
                    message=business.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )
        except Exception as e:
            # TODO: implement error logging
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve business",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_business_by_created_by(self, user):
        try:
            # Check cache first
            cache_key = f"business_by_user:{user.id}"
            cached_business = self.cache.getCache(cache_key)
            if cached_business:
                return APIResponse(
                    success=True,
                    message="Business retrieved successfully.",
                    data=cached_business,
                    status=status.HTTP_200_OK,
                )

            # If not in cache, get from database
            business = Company.objects.filter(created_by=user).first()
            if not business:
                return APIResponse(
                    success=False,
                    message="Business does not exist for the user.",
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Serialize and cache the business data
            serialize_data = BusinessSerializer(business).data
            self.cache.setCache(cache_key, serialize_data)

            return APIResponse(
                success=True,
                message="Business retrieved successfully.",
                data=serialize_data,
                status=status.HTTP_200_OK,
            )
        except Company.DoesNotExist:
            return APIResponse(
                success=False,
                message="Business does not exist for the user.",
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve business",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def update_business(self, slug, user, business_data: Dict[str, Any]) -> APIResponse:
        try:
            existing_business = self.repository.get_business(slug)
            if not existing_business.success:
                return APIResponse(
                    success=False,
                    message=existing_business.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            business = existing_business.data

            if "established_at" in business_data:
                date_response = convert_date_string(
                    business_data["established_at"], "established_at"
                )
                if not date_response.success:
                    return APIResponse(
                        success=False,
                        data=None,
                        message=date_response.message,
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                business_data["established_at"] = date_response.data

            if "languages" in business_data:
                try:
                    self._update_languages(business, business_data["languages"])
                except ValidationError as e:
                    logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        message="Failed to update languages",
                        data=None,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if "business_socials" in business_data:
                try:
                    self._update_social_media(
                        business, business_data["business_socials"]
                    )
                except ValidationError as e:
                    logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        message="Failed to update business_socials",
                        data=None,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if "services" in business_data:
                try:
                    service_response = self._update_services(
                        business, business_data["services"]
                    )
                    if not service_response.success:
                        return APIResponse(
                            success=False,
                            message=service_response.message,
                            data=None,
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                except ValidationError as e:
                    logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        message="Failed to update service",
                        data=None,
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            business_data_copy = copy.deepcopy(business_data)
            related_fields = [
                "services",
                "languages",
                "business_socials",
            ]
            for field in related_fields:
                business_data_copy.pop(field, None)

            response = self.repository.update_business(
                slug=business.slug, data=business_data_copy
            )
            if not response.success:

                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            serialize_data = BusinessSerializer(response.data).data

            business_cache_key = f"business:{slug}"
            user_cache_key = f"business_by_user:{business.created_by.id}"

            self.cache.setCache(business_cache_key, serialize_data)
            self.cache.setCache(user_cache_key, serialize_data)

            if serialize_data.get('id'):
                self.cache.updateCacheItem("businesses", serialize_data['id'], serialize_data)

            self._clear_business_related_caches(
                slug=slug,
                user_id=business.created_by.id,
                business_name=serialize_data.get('name')
            )

            return APIResponse(
                success=True,
                message="Business updated successfully.",
                data=serialize_data,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return APIResponse(
                success=False,
                message=f"Failed to update business: {str(e)}",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    @transaction.atomic
    def _update_languages(self, business, languages):
        language_ids = []
        for language_data in languages:
            response = self.language.create_user_language(language_data)
            if not response.success:
                return RepositoryResponse(
                    success=False,
                    message=response.message,
                    data=None,
                )
            language_ids.append(response.data.id)
        business.languages.set(language_ids)

    @transaction.atomic
    def _update_social_media(self, business, social_media_platforms):
        social_media_ids = []
        for platform_data in social_media_platforms:
            response = self.social_media.create_social_media_platform(platform_data)
            if not response.success:
                return RepositoryResponse(
                    success=False,
                    message=response.message,
                    data=None,
                )
            social_media_ids.append(response.data.id)
        business.business_socials.set(social_media_ids)

    @transaction.atomic
    def _update_services(self, business, services):
        try:
            business.services.clear()

            for service in services:
                category_name = service.get("category_name", "Uncategorized")
                service_name = service.get("name")

                if not service_name:
                    RepositoryResponse(
                        success=False,
                        message="Service name is required",
                        data=None,
                    )

                category, _ = SkillsCategory.objects.get_or_create(name=category_name)

                service_obj, _ = Service.objects.get_or_create(
                    name=service_name, defaults={"category_name": category}
                )

                business.services.add(service_obj)

            return RepositoryResponse(
                success=True,
                data=business.services.all(),
                message="Services updated successfully",
            )
        except ValidationError as ve:
            logging_service.log_error(ve)
            return RepositoryResponse(
                success=False, message="Failed to update services", data=None
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Failed to update services", data=None
            )

    def delete_business(self, user, slug: str) -> APIResponse:
        try:
            business = self.repository.get_business(slug)
            if not business.success:
                return APIResponse(
                    success=False,
                    message=business.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            business_data = business.data
            business_id = business_data.id
            created_by_id = business_data.created_by.id

            delete_result = self.repository.delete_business(slug)
            if delete_result:
                self.cache.removeCacheItem("businesses", business_id)

                self._clear_business_related_caches(
                    slug=slug,
                    user_id=created_by_id,
                    business_name=business_data.name
                )

            return APIResponse(
                success=True,
                message="Business deleted successfully.",
                data=None,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to delete business",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_businesses_by_name(self, name: str) -> APIResponse:
        try:
            cache_key = f"businesses_by_name:{name.lower()}"
            cached_businesses = self.cache.getCache(cache_key)
            if cached_businesses:
                return APIResponse(
                    success=True,
                    message="Businesses retrieved successfully.",
                    data=cached_businesses,
                    status=status.HTTP_200_OK,
                )

            businesses = self.repository.get_businesses_by_name(name)
            if not businesses.success:
                return APIResponse(
                    success=False,
                    message=businesses.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            serialize_data = BusinessSerializer(businesses.data).data

            self.cache.setCache(cache_key, serialize_data, timeout=60 * 30)

            return APIResponse(
                success=True,
                message="Businesses retrieved successfully.",
                data=serialize_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve businesses",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_business_profile_status(self, slug):
        cache_key = f"business_profile_status:{slug}"
        cached_status = self.cache.getCache(cache_key)
        if cached_status:
            return APIResponse(
                success=cached_status['success'],
                message=cached_status['message'],
                data=cached_status['data'],
                status=cached_status['status'],
            )

        fields = {
            1: [
                "name",
                "website_url",
                "description",
                "established_at",
                "number_of_employees",
                "business_address",
                "industry",
            ],
            2: [
                "email",
                "business_socials",
                "languages",
                "phone_number",
                "logo",
                "services",
            ],
        }
        profile_status = StatusChecker.get_status(
            slug, self.repository.get_business, fields, "business"
        )

        status_data = {
            'success': profile_status.success,
            'message': profile_status.message,
            'data': profile_status.data,
            'status': profile_status.status,
        }
        self.cache.setCache(cache_key, status_data, timeout=60 * 5)

        return APIResponse(
            success=profile_status.success,
            message=profile_status.message,
            data=profile_status.data,
            status=profile_status.status,
        )

    def get_all_businesses(self, page=1, page_size=10):
        try:
            cache_key = f"all_businesses_page_{page}_size_{page_size}"
            cached_result = self.cache.getCache(cache_key)
            if cached_result:
                return APIResponse(
                    success=True,
                    message="Businesses retrieved successfully.",
                    data=cached_result,
                    status=status.HTTP_200_OK,
                )

            businesses = self.repository.get_all_businesses()
            if not businesses.success:
                return APIResponse(
                    success=False,
                    message=businesses.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )
            paginator = Paginator(businesses.data, page_size)
            try:
                businesses_page = paginator.page(page)
                serialize_data = BusinessSerializer(businesses_page, many=True).data
            except (EmptyPage, InvalidPage):
                return APIResponse(
                    success=False,
                    message="Invalid page number",
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )

            result_data = {
                "results": serialize_data,
                "total_pages": paginator.num_pages,
                "current_page": page,
                "total_count": paginator.count,
                "has_next": businesses_page.has_next(),
                "has_previous": businesses_page.has_previous(),
            }

            self.cache.setCache(cache_key, result_data, timeout=60 * 15)

            return APIResponse(
                success=True,
                message="Businesses retrieved successfully.",
                data=result_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve businesses",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_business_candidates(self, slug, page=1, page_size=10, search_query=None):
        try:
            search_key = f"_{search_query}" if search_query else ""
            cache_key = f"business_candidates:{slug}_page_{page}_size_{page_size}{search_key}"

            cached_result = self.cache.getCache(cache_key)
            if cached_result:
                return APIResponse(
                    success=True,
                    message="Candidates retrieved successfully.",
                    data=cached_result,
                    status=status.HTTP_200_OK,
                )

            business = self.repository.get_business(slug)
            if not business.success:
                return APIResponse(
                    success=False,
                    message=business.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )
            company = business.data

            applications = Application.objects.filter(job_applied__company_name=company)

            if search_query:
                applications = applications.filter(
                    Q(job_applied__id__icontains=search_query)
                    | Q(job_applied__slug__icontains=search_query)
                )
            candidates = Candidate.objects.filter(
                id__in=applications.values_list("applicant_id", flat=True)
            ).distinct()

            paginator = Paginator(candidates, page_size)
            try:
                candidates_page = paginator.page(page)
                serialize_data = GetCandidateSerializer(candidates_page, many=True).data
            except (EmptyPage, InvalidPage):
                return APIResponse(
                    success=False,
                    message="Invalid page number",
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )

            result_data = {
                "results": serialize_data,
                "total_pages": paginator.num_pages,
                "current_page": page,
                "total_count": paginator.count,
                "has_next": candidates_page.has_next(),
                "has_previous": candidates_page.has_previous(),
            }

            self.cache.setCache(cache_key, result_data, timeout=60 * 10)

            return APIResponse(
                success=True,
                message="Candidates retrieved successfully.",
                data=result_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve candidates",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def _handle_query_without_cache(self, params=None):
        """
        Handles querying the Company model based on provided filter parameters without using cache.

        Args:
            params (dict, optional): A dictionary of filter parameters. Supported keys include:
                - "established_at" (str): Filter companies by established date (expects a date string).
                - "number_of_employees" (int): Filter companies by number of employees.
                - "industry" (str): Filter companies by industry (case-insensitive, partial match).
                - "is_active" (bool or str): Filter companies by active status.
                - "is_verified" (bool or str): Filter companies by verification status.
                - "billing_address" (str): Filter companies by billing address (case-insensitive, partial match).
                - "postal_code" (str): Filter companies by postal code (case-insensitive, partial match).
                - "created_by" (int): Filter companies by the user who created them (ID).

        Returns:
            tuple:
                - bool: True if the query was successful, False otherwise.
                - list or: Serialized list of matching companies if successful,
                  or an  object with error details if an error occurs (e.g., invalid date).
        """

        queryset = Company.objects.all()
        if "name" in params:
            queryset = queryset.filter(name__icontains=params["name"])
        if "description" in params:
            queryset = queryset.filter(description__icontains=params["description"])
        if "website_url" in params:
            queryset = queryset.filter(website_url__icontains=params["website_url"])
        if "established_at" in params:
            established_at = convert_date_string(
                params["established_at"], "established_at"
            )
            if not established_at.success:
                return APIResponse(
                    success=False,
                    message=established_at.message,
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            queryset = queryset.filter(established_at=established_at.data)
        if "number_of_employees" in params:
            queryset = queryset.filter(
                number_of_employees=params["number_of_employees"]
            )
        if "business_address" in params:
            queryset = queryset.filter(
                business_address__icontains=params["business_address"]
            )
        if "industry" in params:
            queryset = queryset.filter(industry__icontains=params["industry"])
        if "is_active" in params:
            is_active = params["is_active"]
            if isinstance(is_active, str):
                is_active = is_active.lower() == "true"
            queryset = queryset.filter(is_active=is_active)
        if "is_verified" in params:
            is_verified = params["is_verified"]
            if isinstance(is_verified, str):
                is_verified = is_verified.lower() == "true"
            queryset = queryset.filter(is_verified=is_verified)

        if "billing_address" in params:
            queryset = queryset.filter(
                business_address__icontains=params["billing_address"]
            )

        if "postal_code" in params:
            queryset = queryset.filter(postal_code__icontains=params["postal_code"])
        if "created_by" in params:
            try:
                user_id = int(params["created_by"])
                queryset = queryset.filter(created_by__id=user_id)
            except ValueError:
                pass

        # serialize the queryset
        serialize_data = BusinessSerializer(queryset, many=True).data

        # cache the results
        for business in serialize_data:
            self.cache.addToCacheList("businesses", business)

        return True, serialize_data

    def _handle_query_with_cache(self, params=None):
        """
        Handles querying companies with caching support.

        Args:
            params (dict, optional): A dictionary of filter parameters. If None, retrieves all companies.
               - "name" (str): Filter companies by phone name (case-insensitive, partial match).
               - "description" (str): Filter companies by description (case-insensitive, partial match).
               - website_url (str): Filter companies by website URL (case-insensitive, partial match).
               - "established_at" (str): Filter companies by established date (expects a date string).
               - "number_of_employees" (int): Filter companies by number of employees.
               - "business_address" (str): Filter companies by business address (case-insensitive, partial match).
               - "industry" (str): Filter companies by industry (case-insensitive, partial match).
               - "is_active" (bool or str): Filter companies by active status.
               - "is_verified" (bool or str): Filter companies by verification status.
               - "billing_address" (str): Filter companies by billing address (case-insensitive, partial match).
               - "postal_code" (str): Filter companies by postal code (case-insensitive, partial match).
               - "created_by" (int): Filter companies by the user who created them (ID).
        Returns:
            tuple:
                - bool: True if the query was successful, False otherwise.
                - list: Serialized list of matching companies if successful,
                  or an APIResponse object with error details if an error occurs (e.g., invalid date).
        """

        businesses = self.cache.getCache("businesses")
        if not businesses:
            return False, []

        if not params:
            return True, businesses

        # Filtering logic for all supported params
        if "name" in params:
            businesses = [
                b
                for b in businesses
                if b.get("name") and params["name"].lower() in b.get("name").lower()
            ]
        if "description" in params:
            businesses = [
                b
                for b in businesses
                if b.get("description")
                and params["description"].lower() in b.get("description").lower()
            ]
        if "website_url" in params:
            businesses = [
                b
                for b in businesses
                if b.get("website_url")
                and params["website_url"].lower() in b.get("website_url").lower()
            ]
        if "established_at" in params:
            # Assuming established_at is stored as a string in the cache
            businesses = [
                b
                for b in businesses
                if b.get("established_at") == params["established_at"]
            ]
        if "number_of_employees" in params:
            try:
                num = int(params["number_of_employees"])
                businesses = [
                    b for b in businesses if b.get("number_of_employees") == num
                ]
            except (ValueError, TypeError):
                businesses = []
        if "business_address" in params:
            businesses = [
                b
                for b in businesses
                if b.get("business_address")
                and params["business_address"].lower()
                in b.get("business_address").lower()
            ]
        if "industry" in params:
            businesses = [
                b
                for b in businesses
                if b.get("industry")
                and params["industry"].lower() in b.get("industry").lower()
            ]
        if "is_active" in params:
            is_active = params["is_active"]
            if isinstance(is_active, str):
                is_active = is_active.lower() == "true"
            businesses = [b for b in businesses if b.get("is_active") == is_active]
        if "is_verified" in params:
            is_verified = params["is_verified"]
            if isinstance(is_verified, str):
                is_verified = is_verified.lower() == "true"
            businesses = [b for b in businesses if b.get("is_verified") == is_verified]
        if "billing_address" in params:
            businesses = [
                b
                for b in businesses
                if b.get("business_address")
                and params["billing_address"].lower()
                in b.get("business_address").lower()
            ]
        if "postal_code" in params:
            businesses = [
                b
                for b in businesses
                if b.get("postal_code")
                and params["postal_code"].lower() in b.get("postal_code").lower()
            ]
        if "created_by" in params:
            try:
                user_id = int(params["created_by"])
                businesses = [b for b in businesses if b.get("created_by") == user_id]
            except (ValueError, TypeError):
                businesses = []

        return True, businesses

    def handle_query(self, params):

        is_in_cache, businesses = self._handle_query_with_cache(params)
        if not is_in_cache:
            is_in_cache, businesses = self._handle_query_without_cache(params)

        # pagination logic
        page = int(params.get("page", 1))
        page_size = int(params.get("page_size", 10))

        # sorting logic
        sort_by = params.get("sort_by", "name")
        sort_order = params.get("sort_order", "asc")
        if sort_order.lower() == "desc":
            businesses = sorted(
                businesses, key=lambda x: x.get(sort_by, ""), reverse=True
            )
        else:
            businesses = sorted(businesses, key=lambda x: x.get(sort_by, ""))

        paginator = Paginator(businesses, page_size)
        try:
            businesses_page = paginator.page(page)
        except (EmptyPage, InvalidPage):
            return APIResponse(
                success=False,
                message="Invalid page number",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = {
            "results": list(businesses_page),
            "total_pages": paginator.num_pages,
            "total_items": paginator.count,
            "current_page": page,
            "total_count": paginator.count,
            "has_next": businesses_page.has_next(),
            "has_previous": businesses_page.has_previous(),
        }
        return APIResponse(
            success=True,
            message="Businesses retrieved successfully.",
            data=data,
            status=status.HTTP_200_OK,
        )
